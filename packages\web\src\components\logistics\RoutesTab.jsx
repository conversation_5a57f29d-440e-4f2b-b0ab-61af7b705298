import React from 'react';
import { motion } from 'framer-motion';
import { MapPin } from 'lucide-react';
import { Button } from '@ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@ui/card';
import { Badge } from '@ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/select';
import { useLanguage } from '@kivu-smartfarm/shared';

const RoutesTab = () => {
  const { t } = useLanguage();

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      className="space-y-6"
    >
      <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="text-green-700">
            {t('logistics.routes.title', 'Route Optimization')}
          </CardTitle>
          <CardDescription>
            {t('logistics.routes.description', 'Plan and optimize delivery routes for maximum efficiency')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="font-semibold text-gray-800 text-sm sm:text-base">
                {t('logistics.routes.popular', 'Popular Routes')}
              </h3>
              <div className="space-y-3">
                {[
                  { from: 'Goma', to: 'Bukavu', distance: '220 km', time: '4h 30m', frequency: '12/week' },
                  { from: 'Bukavu', to: 'Uvira', distance: '120 km', time: '2h 45m', frequency: '8/week' },
                  { from: 'Goma', to: 'Rutshuru', distance: '75 km', time: '1h 30m', frequency: '15/week' }
                ].map((route, index) => (
                  <div key={index} className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-3 sm:p-4">
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                      <div>
                        <p className="font-medium text-gray-800 text-xs sm:text-sm">
                          {route.from} → {route.to}
                        </p>
                        <p className="text-xs text-gray-600">
                          {route.distance} • {route.time}
                        </p>
                      </div>
                      <Badge variant="outline" className="mt-2 sm:mt-0 text-xs">
                        {route.frequency}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold text-gray-800 text-sm sm:text-base">
                {t('logistics.routes.plan_new', 'Plan New Route')}
              </h3>
              <div className="space-y-3">
                <Select>
                  <SelectTrigger className="text-xs sm:text-sm">
                    <SelectValue placeholder={t('logistics.routes.start_location', 'Start Location')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="goma">Goma</SelectItem>
                    <SelectItem value="bukavu">Bukavu</SelectItem>
                    <SelectItem value="uvira">Uvira</SelectItem>
                    <SelectItem value="rutshuru">Rutshuru</SelectItem>
                  </SelectContent>
                </Select>
                <Select>
                  <SelectTrigger className="text-xs sm:text-sm">
                    <SelectValue placeholder={t('logistics.routes.end_location', 'End Location')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="goma">Goma</SelectItem>
                    <SelectItem value="bukavu">Bukavu</SelectItem>
                    <SelectItem value="uvira">Uvira</SelectItem>
                    <SelectItem value="rutshuru">Rutshuru</SelectItem>
                  </SelectContent>
                </Select>
                <Button className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-xs sm:text-sm">
                  {t('logistics.routes.calculate', 'Calculate Route')}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="text-green-700">
            {t('logistics.routes.map', 'Route Map')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gradient-to-br from-green-100 to-emerald-100 rounded-lg h-64 sm:h-96 flex items-center justify-center">
            <div className="text-center">
              <MapPin className="w-12 h-12 sm:w-16 sm:h-16 text-green-600 mx-auto mb-4" />
              <p className="text-gray-600 text-xs sm:text-sm">
                {t('logistics.routes.map_placeholder', 'Interactive route map will be displayed here')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default RoutesTab;
