
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Check, Crown, Star, Zap, Shield, TrendingUp, Users, Globe } from 'lucide-react';
import { But<PERSON> } from '@ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/card';
import { Badge } from '@ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/select';
import Navigation from '../components/Navigation';
import { useLanguage } from '@kivu-smartfarm/shared';
import { toast } from '@ui/use-toast';

const Subscription = () => {
  const { t } = useLanguage();
  const [billingCycle, setBillingCycle] = useState('monthly');
  const [selectedPlan, setSelectedPlan] = useState(null);

  const plans = [
    {
      id: 'basic',
      name: t('subscription.plans.basic.name', 'Basic Farmer'),
      description: t('subscription.plans.basic.description', 'Perfect for small-scale farmers starting their digital journey'),
      monthlyPrice: 15,
      yearlyPrice: 150,
      icon: Users,
      color: 'from-green-500 to-emerald-600',
      features: [
        t('subscription.features.basic_listing', 'Basic product listings (up to 10)'),
        t('subscription.features.market_access', 'Access to local marketplace'),
        t('subscription.features.basic_analytics', 'Basic sales analytics'),
        t('subscription.features.mobile_app', 'Mobile app access'),
        t('subscription.features.community', 'Community forum access'),
        t('subscription.features.email_support', 'Email support')
      ],
      limitations: [
        t('subscription.limitations.ai_analysis', 'Limited AI crop analysis (5/month)'),
        t('subscription.limitations.logistics', 'Basic logistics support')
      ]
    },
    {
      id: 'professional',
      name: t('subscription.plans.professional.name', 'Professional Farmer'),
      description: t('subscription.plans.professional.description', 'Advanced tools for growing agricultural businesses'),
      monthlyPrice: 35,
      yearlyPrice: 350,
      icon: TrendingUp,
      color: 'from-blue-500 to-indigo-600',
      popular: true,
      features: [
        t('subscription.features.unlimited_listings', 'Unlimited product listings'),
        t('subscription.features.regional_market', 'Regional marketplace access'),
        t('subscription.features.advanced_analytics', 'Advanced analytics & insights'),
        t('subscription.features.ai_analysis', 'AI crop analysis (unlimited)'),
        t('subscription.features.quality_cert', 'Quality certification support'),
        t('subscription.features.logistics_priority', 'Priority logistics coordination'),
        t('subscription.features.buyer_matching', 'Smart buyer matching'),
        t('subscription.features.phone_support', 'Phone & email support')
      ],
      limitations: []
    },
    {
      id: 'enterprise',
      name: t('subscription.plans.enterprise.name', 'Enterprise'),
      description: t('subscription.plans.enterprise.description', 'Complete solution for large-scale agricultural operations'),
      monthlyPrice: 75,
      yearlyPrice: 750,
      icon: Crown,
      color: 'from-purple-500 to-pink-600',
      features: [
        t('subscription.features.everything_pro', 'Everything in Professional'),
        t('subscription.features.national_market', 'National marketplace access'),
        t('subscription.features.white_label', 'White-label solutions'),
        t('subscription.features.api_access', 'API access & integrations'),
        t('subscription.features.dedicated_manager', 'Dedicated account manager'),
        t('subscription.features.custom_analytics', 'Custom analytics dashboards'),
        t('subscription.features.bulk_operations', 'Bulk operations management'),
        t('subscription.features.priority_support', '24/7 priority support')
      ],
      limitations: []
    }
  ];

  const addOns = [
    {
      id: 'premium_ai',
      name: t('subscription.addons.premium_ai.name', 'Premium AI Analysis'),
      description: t('subscription.addons.premium_ai.description', 'Advanced AI-powered crop disease detection and treatment recommendations'),
      monthlyPrice: 10,
      yearlyPrice: 100,
      icon: Zap
    },
    {
      id: 'insurance',
      name: t('subscription.addons.insurance.name', 'Crop Insurance'),
      description: t('subscription.addons.insurance.description', 'Comprehensive crop insurance coverage'),
      monthlyPrice: 25,
      yearlyPrice: 250,
      icon: Shield
    },
    {
      id: 'international',
      name: t('subscription.addons.international.name', 'International Markets'),
      description: t('subscription.addons.international.description', 'Access to international buyers and export opportunities'),
      monthlyPrice: 20,
      yearlyPrice: 200,
      icon: Globe
    }
  ];

  const handleSubscribe = (planId) => {
    setSelectedPlan(planId);
    toast({
      title: t('subscription.success.title', 'Subscription Selected'),
      description: t('subscription.success.description', 'Redirecting to payment processing...'),
    });
    
    // Here you would integrate with Stripe or other payment processor
    setTimeout(() => {
      toast({
        title: t('subscription.payment.title', 'Payment Required'),
        description: t('subscription.payment.description', 'Please complete the payment process to activate your subscription.'),
      });
    }, 2000);
  };

  const getPrice = (plan) => {
    return billingCycle === 'monthly' ? plan.monthlyPrice : plan.yearlyPrice;
  };

  const getSavings = (plan) => {
    const monthlyCost = plan.monthlyPrice * 12;
    const yearlyCost = plan.yearlyPrice;
    return monthlyCost - yearlyCost;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-4">
            {t('subscription.title', 'Choose Your Plan')}
          </h1>
          <p className="text-gray-600 text-lg mb-8">
            {t('subscription.subtitle', 'Unlock the full potential of Kivu SMARTFARM with our flexible subscription plans')}
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center space-x-4 mb-8">
            <span className={`font-medium ${billingCycle === 'monthly' ? 'text-indigo-600' : 'text-gray-500'}`}>
              {t('subscription.billing.monthly', 'Monthly')}
            </span>
            <button
              onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                billingCycle === 'yearly' ? 'bg-indigo-600' : 'bg-gray-300'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  billingCycle === 'yearly' ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`font-medium ${billingCycle === 'yearly' ? 'text-indigo-600' : 'text-gray-500'}`}>
              {t('subscription.billing.yearly', 'Yearly')}
            </span>
            {billingCycle === 'yearly' && (
              <Badge className="bg-green-500 text-white ml-2">
                {t('subscription.billing.save', 'Save up to 17%')}
              </Badge>
            )}
          </div>
        </motion.div>

        {/* Subscription Plans */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {plans.map((plan, index) => {
            const IconComponent = plan.icon;
            return (
              <motion.div
                key={plan.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                delay={index * 0.1}
                whileHover={{ scale: 1.05 }}
                className={`relative bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border-0 overflow-hidden ${
                  plan.popular ? 'ring-2 ring-indigo-500' : ''
                }`}
              >
                {plan.popular && (
                  <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-center py-2 text-sm font-medium">
                    {t('subscription.popular', 'Most Popular')}
                  </div>
                )}
                
                <div className={`bg-gradient-to-r ${plan.color} p-6 text-white ${plan.popular ? 'pt-12' : ''}`}>
                  <div className="flex items-center justify-between mb-4">
                    <IconComponent className="w-8 h-8" />
                    {plan.popular && <Star className="w-6 h-6 text-yellow-300" />}
                  </div>
                  <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                  <p className="text-white/90 text-sm">{plan.description}</p>
                </div>

                <div className="p-6">
                  <div className="mb-6">
                    <div className="flex items-baseline">
                      <span className="text-4xl font-bold text-gray-800">
                        ${getPrice(plan)}
                      </span>
                      <span className="text-gray-600 ml-2">
                        /{billingCycle === 'monthly' ? t('subscription.per_month', 'month') : t('subscription.per_year', 'year')}
                      </span>
                    </div>
                    {billingCycle === 'yearly' && (
                      <p className="text-green-600 text-sm font-medium mt-1">
                        {t('subscription.save_amount', 'Save ${{amount}} per year', { amount: getSavings(plan) })}
                      </p>
                    )}
                  </div>

                  <div className="space-y-3 mb-6">
                    {plan.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-start space-x-3">
                        <Check className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <span className="text-gray-700 text-sm">{feature}</span>
                      </div>
                    ))}
                    {plan.limitations.map((limitation, limitIndex) => (
                      <div key={limitIndex} className="flex items-start space-x-3">
                        <div className="w-5 h-5 flex-shrink-0 mt-0.5">
                          <div className="w-3 h-3 bg-gray-300 rounded-full mx-auto mt-1"></div>
                        </div>
                        <span className="text-gray-500 text-sm">{limitation}</span>
                      </div>
                    ))}
                  </div>

                  <Button
                    onClick={() => handleSubscribe(plan.id)}
                    className={`w-full bg-gradient-to-r ${plan.color} hover:opacity-90 text-white font-medium py-3`}
                    disabled={selectedPlan === plan.id}
                  >
                    {selectedPlan === plan.id 
                      ? t('subscription.processing', 'Processing...') 
                      : t('subscription.get_started', 'Get Started')
                    }
                  </Button>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Add-ons Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          delay={0.4}
          className="mb-12"
        >
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-8">
            {t('subscription.addons.title', 'Enhance Your Plan')}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {addOns.map((addon) => {
              const IconComponent = addon.icon;
              return (
                <Card key={addon.id} className="bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-shadow">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg">
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{addon.name}</CardTitle>
                        <CardDescription className="text-sm">{addon.description}</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-2xl font-bold text-gray-800">
                          ${billingCycle === 'monthly' ? addon.monthlyPrice : addon.yearlyPrice}
                        </span>
                        <span className="text-gray-600 text-sm ml-1">
                          /{billingCycle === 'monthly' ? t('subscription.per_month', 'month') : t('subscription.per_year', 'year')}
                        </span>
                      </div>
                      <Button variant="outline" size="sm">
                        {t('subscription.add', 'Add')}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </motion.div>

        {/* FAQ Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          delay={0.6}
          className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-lg p-8"
        >
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-8">
            {t('subscription.faq.title', 'Frequently Asked Questions')}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">
                  {t('subscription.faq.q1', 'Can I change my plan anytime?')}
                </h3>
                <p className="text-gray-600 text-sm">
                  {t('subscription.faq.a1', 'Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.')}
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">
                  {t('subscription.faq.q2', 'Is there a free trial?')}
                </h3>
                <p className="text-gray-600 text-sm">
                  {t('subscription.faq.a2', 'We offer a 14-day free trial for all new users to explore our platform features.')}
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">
                  {t('subscription.faq.q3', 'What payment methods do you accept?')}
                </h3>
                <p className="text-gray-600 text-sm">
                  {t('subscription.faq.a3', 'We accept credit cards, mobile money (M-Pesa, Orange Money), and bank transfers.')}
                </p>
              </div>
            </div>
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">
                  {t('subscription.faq.q4', 'Can I cancel my subscription?')}
                </h3>
                <p className="text-gray-600 text-sm">
                  {t('subscription.faq.a4', 'Yes, you can cancel your subscription at any time. You will continue to have access until the end of your billing period.')}
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">
                  {t('subscription.faq.q5', 'Do you offer discounts for cooperatives?')}
                </h3>
                <p className="text-gray-600 text-sm">
                  {t('subscription.faq.a5', 'Yes, we offer special pricing for farmer cooperatives and bulk subscriptions. Contact our sales team for details.')}
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">
                  {t('subscription.faq.q6', 'Is my data secure?')}
                </h3>
                <p className="text-gray-600 text-sm">
                  {t('subscription.faq.a6', 'Absolutely. We use enterprise-grade security measures to protect your data and comply with international privacy standards.')}
                </p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Subscription;


