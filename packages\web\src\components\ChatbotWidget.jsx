import React, { useState } from 'react';
import ChatBot from 'react-chatbot-kit';
import 'react-chatbot-kit/build/main.css';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageSquare, X } from 'lucide-react';
import { Button } from '@ui/button';
import { useLanguage } from '@kivu-smartfarm/shared';
import { cn } from '@/lib/utils';

const ChatbotWidget = () => {
  const [showChatbot, setShowChatbot] = useState(false);
  const { t, language } = useLanguage();

  const config = {
    initialMessages: [
      {
        id: 1,
        message: t('chatbot.welcome', 'Welcome to Kivu SMARTFARM! How can I assist you today?'),
        widget: 'options',
      },
    ],
    botName: t('chatbot.botName', 'KivuSMART Assistant'),
    customStyles: {
      botMessageBox: {
        backgroundColor: '#22c55e',
        backgroundImage: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
        padding: '14px 18px',
        borderRadius: '16px 16px 2px 16px',
        marginBottom: '12px',
        color: '#ffffff',
        fontSize: '15px',
        lineHeight: '1.5',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        maxWidth: '85%',
      },
      chatButton: {
        backgroundColor: '#22c55e',
        backgroundImage: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
        padding: '12px 24px',
        borderRadius: '24px',
        color: '#ffffff',
        fontSize: '15px',
        fontWeight: '500',
        border: 'none',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        margin: '4px 8px',
        '&:hover': {
          transform: 'translateY(-1px)',
          boxShadow: '0 4px 6px rgba(0,0,0,0.15)',
        },
      },
      userMessageBox: {
        backgroundColor: '#ffffff',
        padding: '14px 18px',
        borderRadius: '16px 16px 16px 2px',
        marginBottom: '12px',
        marginLeft: 'auto',
        color: '#1f2937',
        fontSize: '15px',
        lineHeight: '1.5',
        boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
        border: '1px solid #e5e7eb',
        maxWidth: '85%',
      },
    },
    widgets: [
      {
        widgetName: 'options',
        widgetFunc: (props) => <Options {...props} />,
        mapStateToProps: ['messages'],
      },
    ],
    state: {
      language,
    },
  };

  const handleToggleChatbot = () => {
    setShowChatbot(prev => !prev);
  };

  const ActionProvider = ({ createChatBotMessage, setState, children }) => {
    const handleOption = (option) => {
      let botMessage;
      switch (option) {
        case 'marketplace':
          botMessage = createChatBotMessage(
            t('chatbot.responses.marketplace', [
              "I'd be happy to help you browse our marketplace! Here's what you can do:",
              "• View available products",
              "• Filter by category or region",
              "• Check product quality and certifications",
              "• Contact sellers directly",
              "\nWould you like me to help you find specific products?"
            ].join('\n')),
            { widget: 'marketplace_options' }
          );
          break;
        case 'support':
          botMessage = createChatBotMessage(
            t('chatbot.responses.support', [
              "I'm here to help! You can get support through:",
              "• Email: <EMAIL>",
              "• Phone: +243 XXX XXX XXX",
              "• Live Chat (available 9AM-5PM)",
              "\nWhat kind of support do you need?"
            ].join('\n')),
            { widget: 'support_options' }
          );
          break;
        case 'pricing':
          botMessage = createChatBotMessage(
            t('chatbot.responses.pricing', [
              "We offer flexible subscription plans:",
              "• Basic Farmer: $15/month",
              "• Professional: $35/month",
              "• Enterprise: $75/month",
              "\nWould you like to see detailed features for each plan?"
            ].join('\n')),
            { widget: 'pricing_options' }
          );
          break;
        case 'aiAnalysis':
          botMessage = createChatBotMessage(
            t('chatbot.responses.aiAnalysis', [
              "Our AI Crop Analysis tool can help you:",
              "• Detect diseases early",
              "• Get treatment recommendations",
              "• Track crop health over time",
              "• Predict harvest yields",
              "\nWould you like to try it now?"
            ].join('\n')),
            { widget: 'ai_options' }
          );
          break;
        default:
          botMessage = createChatBotMessage(
            t('chatbot.responses.default', "I'm here to help! Please choose one of the options above or ask me a specific question.")
          );
      }
      
      setState((prev) => ({
        ...prev,
        messages: [...prev.messages, botMessage],
      }));
    };

    return (
      <div>
        {React.Children.map(children, (child) => {
          return React.cloneElement(child, {
            actions: {
              handleOption,
            },
          });
        })}
      </div>
    );
  };

  const MessageParser = ({ children, actions }) => {
    const parse = (message) => {
      const lowerCaseMessage = message.toLowerCase();
      
      // Common patterns
      const marketplacePatterns = ['market', 'buy', 'sell', 'product', 'price', 'shop'];
      const supportPatterns = ['help', 'support', 'contact', 'issue', 'problem'];
      const pricingPatterns = ['price', 'cost', 'subscription', 'plan', 'payment'];
      const aiPatterns = ['ai', 'analysis', 'disease', 'crop', 'detect'];
      
      if (marketplacePatterns.some(pattern => lowerCaseMessage.includes(pattern))) {
        actions.handleOption('marketplace');
      } else if (supportPatterns.some(pattern => lowerCaseMessage.includes(pattern))) {
        actions.handleOption('support');
      } else if (pricingPatterns.some(pattern => lowerCaseMessage.includes(pattern))) {
        actions.handleOption('pricing');
      } else if (aiPatterns.some(pattern => lowerCaseMessage.includes(pattern))) {
        actions.handleOption('aiAnalysis');
      } else {
        actions.handleOption('default');
      }
    };

    return (
      <div>
        {React.Children.map(children, (child) => {
          return React.cloneElement(child, {
            parse: parse,
            actions,
          });
        })}
      </div>
    );
  };
  
  const Options = (props) => {
    const options = [
      {
        text: t('chatbot.options.marketplace', 'Browse Products'),
        handler: () => props.actions.handleOption('marketplace'),
        icon: '🛒',
        id: 1,
      },
      {
        text: t('chatbot.options.support', 'Get Support'),
        handler: () => props.actions.handleOption('support'),
        icon: '🤝',
        id: 2,
      },
      {
        text: t('chatbot.options.pricing', 'View Pricing'),
        handler: () => props.actions.handleOption('pricing'),
        icon: '💰',
        id: 3,
      },
      {
        text: t('chatbot.options.aiAnalysis', 'AI Crop Analysis'),
        handler: () => props.actions.handleOption('aiAnalysis'),
        icon: '🌾',
        id: 4,
      },
    ];

    return (
      <div className="chatbot-options-container">
        {options.map((option) => (
          <button
            key={option.id}
            onClick={option.handler}
            className="chatbot-option-button"
          >
            <span className="option-icon">{option.icon}</span>
            {option.text}
          </button>
        ))}
      </div>
    );
  };

  return (
    <>
      <style jsx global>{`
        .chatbot-options-container {
          display: grid;
          grid-template-columns: 1fr;
          gap: 8px;
          padding: 12px;
          background: linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(249,250,251,0.9));
          backdrop-filter: blur(8px);
          border-radius: 12px;
        }
        
        .chatbot-option-button {
          display: flex;
          align-items: center;
          padding: 14px;
          background: linear-gradient(145deg, #ffffff, #f8fafc);
          border: 1px solid rgba(229, 231, 235, 0.5);
          border-radius: 12px;
          color: #374151;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .chatbot-option-button:hover {
          background: linear-gradient(145deg, #f8fafc, #ffffff);
          transform: translateY(-2px);
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .option-icon {
          margin-right: 10px;
          font-size: 18px;
        }
        
        .react-chatbot-kit-chat-container {
          border-radius: 16px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
          border: 1px solid rgba(229, 231, 235, 0.5);
          background: linear-gradient(180deg, #ffffff, #f8fafc);
          overflow: hidden;
        }
        
        .react-chatbot-kit-chat-header {
          background: linear-gradient(135deg, #22c55e, #16a34a);
          color: white;
          padding: 20px;
          font-weight: 600;
          letter-spacing: 0.02em;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .react-chatbot-kit-chat-input-container {
          padding: 16px;
          background: linear-gradient(to top, #f9fafb, #ffffff);
          border-top: 1px solid rgba(229, 231, 235, 0.5);
        }
        
        .react-chatbot-kit-chat-input {
          border-radius: 24px;
          border: 2px solid rgba(209, 213, 219, 0.5);
          padding: 12px 20px;
          font-size: 14px;
          background: rgba(255, 255, 255, 0.9);
          backdrop-filter: blur(4px);
          width: calc(100% - 50px);
          transition: all 0.2s ease;
        }
        
        .react-chatbot-kit-chat-input:focus {
          outline: none;
          border-color: #22c55e;
          box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
        }
        
        .react-chatbot-kit-chat-btn-send {
          background: linear-gradient(135deg, #22c55e, #16a34a);
          border-radius: 50%;
          width: 38px;
          height: 38px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 4px rgba(22, 163, 74, 0.2);
          transition: all 0.2s ease;
          margin-left: 8px;
        }
        
        .react-chatbot-kit-chat-btn-send:hover {
          background: linear-gradient(135deg, #16a34a, #15803d);
          transform: translateY(-1px);
          box-shadow: 0 4px 6px rgba(22, 163, 74, 0.3);
        }

        .react-chatbot-kit-chat-message-container {
          padding: 16px;
          background: linear-gradient(180deg, rgba(255,255,255,0.8), rgba(249,250,251,0.9));
        }

        .react-chatbot-kit-chat-bot-message {
          background: linear-gradient(135deg, #22c55e, #16a34a);
          padding: 12px 16px;
          border-radius: 16px 16px 2px 16px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          max-width: 85%;
          margin-bottom: 12px;
        }

        .react-chatbot-kit-chat-bot-message-arrow {
          display: none;
        }

        .react-chatbot-kit-chat-bot-avatar-container {
          background: linear-gradient(135deg, #15803d, #16a34a);
          border-radius: 50%;
          margin-right: 8px;
        }

        .react-chatbot-kit-chat-bot-avatar-icon {
          fill: white;
        }

        .react-chatbot-kit-user-chat-message {
          background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
          padding: 12px 16px;
          border-radius: 16px 16px 16px 2px;
          color: #1f2937;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
          max-width: 85%;
          margin-bottom: 12px;
          margin-left: auto;
        }

        .react-chatbot-kit-user-avatar-container {
          display: none;
        }
      `}</style>
      
      <div className="fixed bottom-4 right-4 z-50">
        <AnimatePresence>
          {showChatbot && (
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 20, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="mb-4 rounded-2xl shadow-xl bg-white overflow-hidden"
              style={{ width: '380px', height: '560px' }}
            >
              <div className="bg-gradient-to-r from-green-500 to-green-600 p-4 flex justify-between items-center">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center">
                    <MessageSquare size={18} className="text-white" />
                  </div>
                  <h3 className="text-white font-semibold text-lg">{config.botName}</h3>
                </div>
                <button
                  onClick={handleToggleChatbot}
                  className="text-white/80 hover:text-white hover:bg-white/10 p-2 rounded-full transition-colors"
                >
                  <X size={20} />
                </button>
              </div>
              <div className="h-[calc(560px-72px)] relative">
                <ChatBot
                  config={config}
                  messageParser={MessageParser}
                  actionProvider={ActionProvider}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        <Button
          onClick={handleToggleChatbot}
          className={cn(
            'rounded-full w-14 h-14 flex items-center justify-center',
            'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',
            'text-white shadow-lg transition-all duration-200 ease-in-out',
            'hover:shadow-xl hover:scale-105',
            !showChatbot && 'animate-bounce-subtle'
          )}
        >
          <MessageSquare size={24} />
        </Button>
      </div>
    </>
  );
};

export default ChatbotWidget;
