import React from 'react';
import { motion } from 'framer-motion';
import { Phone } from 'lucide-react';
import { Button } from '@ui/button';
import { Badge } from '@ui/badge';
import { useLanguage } from '@kivu-smartfarm/shared';

const driversData = [
  {
    id: 1,
    name: '<PERSON>',
    phone: '+243 123 456 789',
    vehicle: 'Toyota Hiace',
    plateNumber: 'CD 1234 AB',
    status: 'active',
    currentLocation: 'En route to Bukavu',
    rating: 4.8,
    completedDeliveries: 156
  },
  {
    id: 2,
    name: '<PERSON>',
    phone: '+243 987 654 321',
    vehicle: 'Isuzu NPR',
    plateNumber: 'CD 5678 CD',
    status: 'available',
    currentLocation: 'Goma Depot',
    rating: 4.9,
    completedDeliveries: 203
  },
  {
    id: 3,
    name: '<PERSON>',
    phone: '+243 555 123 456',
    vehicle: 'Mitsubishi Canter',
    plateNumber: 'CD 9012 EF',
    status: 'maintenance',
    currentLocation: 'Service Center',
    rating: 4.7,
    completedDeliveries: 89
  }
];

const DriversTab = () => {
  const { t } = useLanguage();

  const getDriverStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'available': return 'bg-blue-500';
      case 'maintenance': return 'bg-orange-500';
      case 'offline': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      className="space-y-6"
    >
      <div className="flex flex-col sm:flex-row justify-between items-center">
        <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-4 sm:mb-0">
          {t('logistics.drivers.title', 'Driver Management')}
        </h2>
        <Button className="bg-gradient-to-r from-green-600 to-emerald-600 w-full sm:w-auto">
          {t('logistics.drivers.add', 'Add New Driver')}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {driversData.map((driver) => (
          <motion.div
            key={driver.id}
            whileHover={{ scale: 1.05 }}
            className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border-0 p-6"
          >
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-md sm:text-lg font-bold text-gray-800">{driver.name}</h3>
                <p className="text-xs text-gray-600 flex items-center">
                  <Phone className="w-3 h-3 mr-1" />
                  {driver.phone}
                </p>
              </div>
              <Badge className={`${getDriverStatusColor(driver.status)} text-white text-xs`}>
                {t(`logistics.driver_status.${driver.status}`, driver.status)}
              </Badge>
            </div>

            <div className="space-y-2 mb-4 text-xs sm:text-sm">
              <p className="text-gray-600">
                <span className="font-medium">{t('logistics.vehicle', 'Vehicle')}:</span> {driver.vehicle}
              </p>
              <p className="text-gray-600">
                <span className="font-medium">{t('logistics.plate', 'Plate')}:</span> {driver.plateNumber}
              </p>
              <p className="text-gray-600">
                <span className="font-medium">{t('logistics.location', 'Location')}:</span> {driver.currentLocation}
              </p>
              <p className="text-gray-600">
                <span className="font-medium">{t('logistics.rating', 'Rating')}:</span> ⭐ {driver.rating}
              </p>
              <p className="text-gray-600">
                <span className="font-medium">{t('logistics.deliveries', 'Deliveries')}:</span> {driver.completedDeliveries}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
              <Button variant="outline" size="sm" className="flex-1 text-xs">
                {t('logistics.contact', 'Contact')}
              </Button>
              <Button size="sm" className="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 text-xs">
                {t('logistics.assign', 'Assign')}
              </Button>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default DriversTab;
