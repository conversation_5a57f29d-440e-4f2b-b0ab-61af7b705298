import {
  require_react
} from "./chunk-PB3AIZ72.js";
import {
  __commonJS
} from "./chunk-ROME4SDB.js";

// ../../node_modules/.pnpm/react-conditionally-render@1.0.2/node_modules/react-conditionally-render/dist/index.js
var require_dist = __commonJS({
  "../../node_modules/.pnpm/react-conditionally-render@1.0.2/node_modules/react-conditionally-render/dist/index.js"(exports, module) {
    (() => {
      "use strict";
      var e = { d: (o2, r2) => {
        for (var n in r2)
          e.o(r2, n) && !e.o(o2, n) && Object.defineProperty(o2, n, { enumerable: true, get: r2[n] });
      }, o: (e2, o2) => Object.prototype.hasOwnProperty.call(e2, o2), r: (e2) => {
        "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e2, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e2, "__esModule", { value: true });
      } }, o = {};
      e.r(o), e.d(o, { default: () => r });
      const r = function(e2) {
        var o2 = e2.condition, r2 = e2.show, n = e2.elseShow, t = function(e3) {
          return "function" == typeof e3;
        }, u = function(e3) {
          return e3() || (console.warn("Nothing was returned from your render function. Please make sure you are returning a valid React element."), null);
        };
        return o2 ? t(r2) ? u(r2) : r2 : !o2 && n ? t(n) ? u(n) : n : null;
      };
      module.exports = o;
    })();
  }
});

// ../../node_modules/.pnpm/react-chatbot-kit@2.2.2/node_modules/react-chatbot-kit/build/index.js
var require_build = __commonJS({
  "../../node_modules/.pnpm/react-chatbot-kit@2.2.2/node_modules/react-chatbot-kit/build/index.js"(exports, module) {
    (() => {
      "use strict";
      var e = { n: (t2) => {
        var r2 = t2 && t2.__esModule ? () => t2.default : () => t2;
        return e.d(r2, { a: r2 }), r2;
      }, d: (t2, r2) => {
        for (var a2 in r2)
          e.o(r2, a2) && !e.o(t2, a2) && Object.defineProperty(t2, a2, { enumerable: true, get: r2[a2] });
      }, o: (e2, t2) => Object.prototype.hasOwnProperty.call(e2, t2), r: (e2) => {
        "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e2, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e2, "__esModule", { value: true });
      } }, t = {};
      e.r(t), e.d(t, { Chatbot: () => B, createChatBotMessage: () => i, createClientMessage: () => u, createCustomMessage: () => l, default: () => H, useChatbot: () => T });
      const r = require_react();
      var a = e.n(r);
      const n = require_dist();
      var o = e.n(n), s = function() {
        return s = Object.assign || function(e2) {
          for (var t2, r2 = 1, a2 = arguments.length; r2 < a2; r2++)
            for (var n2 in t2 = arguments[r2])
              Object.prototype.hasOwnProperty.call(t2, n2) && (e2[n2] = t2[n2]);
          return e2;
        }, s.apply(this, arguments);
      }, c = function(e2, t2) {
        return { message: e2, type: t2, id: Math.round(Date.now() * Math.random()) };
      }, i = function(e2, t2) {
        return s(s(s({}, c(e2, "bot")), t2), { loading: true });
      }, l = function(e2, t2, r2) {
        return s(s({}, c(e2, t2)), r2);
      }, u = function(e2, t2) {
        return s(s({}, c(e2, "user")), t2);
      }, m = function(e2) {
        for (var t2 = [], r2 = 1; r2 < arguments.length; r2++)
          t2[r2 - 1] = arguments[r2];
        if (e2)
          return e2.apply(void 0, t2);
      };
      function g() {
        return g = Object.assign || function(e2) {
          for (var t2 = 1; t2 < arguments.length; t2++) {
            var r2 = arguments[t2];
            for (var a2 in r2)
              Object.prototype.hasOwnProperty.call(r2, a2) && (e2[a2] = r2[a2]);
          }
          return e2;
        }, g.apply(this, arguments);
      }
      const d = ({ styles: e2 = {}, ...t2 }) => a().createElement("svg", g({ xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 512 512" }, t2), a().createElement("path", { d: "M256 288c79.5 0 144-64.5 144-144S335.5 0 256 0 112 64.5 112 144s64.5 144 144 144zm128 32h-55.1c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16H128C57.3 320 0 377.3 0 448v16c0 26.5 21.5 48 48 48h416c26.5 0 48-21.5 48-48v-16c0-70.7-57.3-128-128-128z" })), f = function(e2) {
        var t2 = e2.message, r2 = e2.customComponents;
        return a().createElement("div", { className: "react-chatbot-kit-user-chat-message-container" }, a().createElement(o(), { condition: !!r2.userChatMessage, show: m(r2.userChatMessage, { message: t2 }), elseShow: a().createElement("div", { className: "react-chatbot-kit-user-chat-message" }, t2, a().createElement("div", { className: "react-chatbot-kit-user-chat-message-arrow" })) }), a().createElement(o(), { condition: !!r2.userAvatar, show: m(r2.userAvatar), elseShow: a().createElement("div", { className: "react-chatbot-kit-user-avatar" }, a().createElement("div", { className: "react-chatbot-kit-user-avatar-container" }, a().createElement(d, { className: "react-chatbot-kit-user-avatar-icon" }))) }));
      }, h = function() {
        return a().createElement("div", { className: "react-chatbot-kit-chat-bot-avatar" }, a().createElement("div", { className: "react-chatbot-kit-chat-bot-avatar-container" }, a().createElement("p", { className: "react-chatbot-kit-chat-bot-avatar-letter" }, "B")));
      }, p = function() {
        return a().createElement("div", { className: "chatbot-loader-container" }, a().createElement("svg", { id: "dots", width: "50px", height: "21px", viewBox: "0 0 132 58", version: "1.1", xmlns: "http://www.w3.org/2000/svg" }, a().createElement("g", { stroke: "none", fill: "none" }, a().createElement("g", { id: "chatbot-loader", fill: "#fff" }, a().createElement("circle", { id: "chatbot-loader-dot1", cx: "25", cy: "30", r: "13" }), a().createElement("circle", { id: "chatbot-loader-dot2", cx: "65", cy: "30", r: "13" }), a().createElement("circle", { id: "chatbot-loader-dot3", cx: "105", cy: "30", r: "13" })))));
      };
      var v = function() {
        return v = Object.assign || function(e2) {
          for (var t2, r2 = 1, a2 = arguments.length; r2 < a2; r2++)
            for (var n2 in t2 = arguments[r2])
              Object.prototype.hasOwnProperty.call(t2, n2) && (e2[n2] = t2[n2]);
          return e2;
        }, v.apply(this, arguments);
      };
      const y = function(e2) {
        var t2 = e2.message, n2 = e2.withAvatar, s2 = void 0 === n2 || n2, c2 = e2.loading, i2 = e2.messages, l2 = e2.customComponents, u2 = e2.setState, g2 = e2.customStyles, d2 = e2.delay, f2 = e2.id, y2 = (0, r.useState)(false), b2 = y2[0], w2 = y2[1];
        (0, r.useEffect)(function() {
          var e3;
          return function(t3, r2) {
            var a2 = 750;
            d2 && (a2 += d2), e3 = setTimeout(function() {
              var e4 = function(e5, t4, r3) {
                if (r3 || 2 === arguments.length)
                  for (var a3, n3 = 0, o2 = t4.length; n3 < o2; n3++)
                    !a3 && n3 in t4 || (a3 || (a3 = Array.prototype.slice.call(t4, 0, n3)), a3[n3] = t4[n3]);
                return e5.concat(a3 || Array.prototype.slice.call(t4));
              }([], t3, true).find(function(e5) {
                return e5.id === f2;
              });
              e4 && (e4.loading = false, e4.delay = void 0, r2(function(t4) {
                var r3 = t4.messages, a3 = r3.findIndex(function(e5) {
                  return e5.id === f2;
                });
                return r3[a3] = e4, v(v({}, t4), { messages: r3 });
              }));
            }, a2);
          }(i2, u2), function() {
            clearTimeout(e3);
          };
        }, [d2, f2]), (0, r.useEffect)(function() {
          d2 ? setTimeout(function() {
            return w2(true);
          }, d2) : w2(true);
        }, [d2]);
        var E2 = { backgroundColor: "" }, P2 = { borderRightColor: "" };
        return g2 && (E2.backgroundColor = g2.backgroundColor, P2.borderRightColor = g2.backgroundColor), a().createElement(o(), { condition: b2, show: a().createElement("div", { className: "react-chatbot-kit-chat-bot-message-container" }, a().createElement(o(), { condition: s2, show: a().createElement(o(), { condition: !!(null == l2 ? void 0 : l2.botAvatar), show: m(null == l2 ? void 0 : l2.botAvatar), elseShow: a().createElement(h, null) }) }), a().createElement(o(), { condition: !!(null == l2 ? void 0 : l2.botChatMessage), show: m(null == l2 ? void 0 : l2.botChatMessage, { message: t2, loader: a().createElement(p, null) }), elseShow: a().createElement("div", { className: "react-chatbot-kit-chat-bot-message", style: E2 }, a().createElement(o(), { condition: c2, show: a().createElement(p, null), elseShow: a().createElement("span", null, t2) }), a().createElement(o(), { condition: s2, show: a().createElement("div", { className: "react-chatbot-kit-chat-bot-message-arrow", style: P2 }) })) })) });
      };
      function b() {
        return b = Object.assign || function(e2) {
          for (var t2 = 1; t2 < arguments.length; t2++) {
            var r2 = arguments[t2];
            for (var a2 in r2)
              Object.prototype.hasOwnProperty.call(r2, a2) && (e2[a2] = r2[a2]);
          }
          return e2;
        }, b.apply(this, arguments);
      }
      const w = ({ styles: e2 = {}, ...t2 }) => a().createElement("svg", b({ xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 512 512" }, t2), a().createElement("path", { d: "M476 3.2L12.5 270.6c-18.1 10.4-15.8 35.6 2.2 43.2L121 358.4l287.3-253.2c5.5-4.9 13.3 2.6 8.6 8.3L176 407v80.5c0 23.6 28.5 32.9 42.5 15.8L282 426l124.6 52.2c14.2 6 30.4-2.9 33-18.2l72-432C515 7.8 493.3-6.8 476 3.2z" }));
      var E = function() {
        return E = Object.assign || function(e2) {
          for (var t2, r2 = 1, a2 = arguments.length; r2 < a2; r2++)
            for (var n2 in t2 = arguments[r2])
              Object.prototype.hasOwnProperty.call(t2, n2) && (e2[n2] = t2[n2]);
          return e2;
        }, E.apply(this, arguments);
      }, P = function(e2, t2, r2) {
        if (r2 || 2 === arguments.length)
          for (var a2, n2 = 0, o2 = t2.length; n2 < o2; n2++)
            !a2 && n2 in t2 || (a2 || (a2 = Array.prototype.slice.call(t2, 0, n2)), a2[n2] = t2[n2]);
        return e2.concat(a2 || Array.prototype.slice.call(t2));
      };
      const S = function(e2) {
        var t2 = e2.state, n2 = e2.setState, s2 = e2.widgetRegistry, i2 = e2.messageParser, l2 = e2.parse, u2 = e2.customComponents, m2 = e2.actionProvider, g2 = e2.botName, d2 = e2.customStyles, h2 = e2.headerText, p2 = e2.customMessages, v2 = e2.placeholderText, b2 = e2.validator, S2 = e2.disableScrollToBottom, O2 = e2.messageHistory, k2 = e2.actions, M2 = e2.messageContainerRef, C2 = t2.messages, N2 = (0, r.useState)(""), x2 = N2[0], j2 = N2[1], T2 = function() {
          setTimeout(function() {
            var e3;
            M2.current && (M2.current.scrollTop = null === (e3 = null == M2 ? void 0 : M2.current) || void 0 === e3 ? void 0 : e3.scrollHeight);
          }, 50);
        };
        (0, r.useEffect)(function() {
          S2 || T2();
        });
        var A2 = function() {
          n2(function(e3) {
            return E(E({}, e3), { messages: P(P([], e3.messages, true), [c(x2, "user")], false) });
          }), T2(), j2("");
        }, B2 = { backgroundColor: "" };
        d2 && d2.chatButton && (B2.backgroundColor = d2.chatButton.backgroundColor);
        var H2 = "Conversation with " + g2;
        h2 && (H2 = h2);
        var I = "Write your message here";
        return v2 && (I = v2), a().createElement("div", { className: "react-chatbot-kit-chat-container" }, a().createElement("div", { className: "react-chatbot-kit-chat-inner-container" }, a().createElement(o(), { condition: !!u2.header, show: u2.header && u2.header(m2), elseShow: a().createElement("div", { className: "react-chatbot-kit-chat-header" }, H2) }), a().createElement("div", { className: "react-chatbot-kit-chat-message-container", ref: M2 }, a().createElement(o(), { condition: "string" == typeof O2 && Boolean(O2), show: a().createElement("div", { dangerouslySetInnerHTML: { __html: O2 } }) }), C2.map(function(e3, r2) {
          return "bot" === e3.type ? a().createElement(a().Fragment, { key: e3.id }, function(e4, r3) {
            var c2;
            c2 = e4.withAvatar ? e4.withAvatar : function(e5, t3) {
              if (0 === t3)
                return true;
              var r4 = e5[t3 - 1];
              return !("bot" === r4.type && !r4.widget);
            }(C2, r3);
            var i3 = E(E({}, e4), { setState: n2, state: t2, customComponents: u2, widgetRegistry: s2, messages: C2, actions: k2 });
            if (e4.widget) {
              var l3 = s2.getWidget(i3.widget, E(E({}, t2), { scrollIntoView: T2, payload: e4.payload, actions: k2 }));
              return a().createElement(a().Fragment, null, a().createElement(y, E({ customStyles: d2.botMessageBox, withAvatar: c2 }, i3, { key: e4.id })), a().createElement(o(), { condition: !i3.loading, show: l3 || null }));
            }
            return a().createElement(y, E({ customStyles: d2.botMessageBox, key: e4.id, withAvatar: c2 }, i3, { customComponents: u2, messages: C2, setState: n2 }));
          }(e3, r2)) : "user" === e3.type ? a().createElement(a().Fragment, { key: e3.id }, function(e4) {
            var r3 = s2.getWidget(e4.widget, E(E({}, t2), { scrollIntoView: T2, payload: e4.payload, actions: k2 }));
            return a().createElement(a().Fragment, null, a().createElement(f, { message: e4.message, key: e4.id, customComponents: u2 }), r3 || null);
          }(e3)) : function(e4, t3) {
            return !!t3[e4.type];
          }(e3, p2) ? a().createElement(a().Fragment, { key: e3.id }, function(e4) {
            var r3 = p2[e4.type], o2 = { setState: n2, state: t2, scrollIntoView: T2, actionProvider: m2, payload: e4.payload, actions: k2 };
            if (e4.widget) {
              var c2 = s2.getWidget(e4.widget, E(E({}, t2), { scrollIntoView: T2, payload: e4.payload, actions: k2 }));
              return a().createElement(a().Fragment, null, r3(o2), c2 || null);
            }
            return r3(o2);
          }(e3)) : void 0;
        }), a().createElement("div", { style: { paddingBottom: "15px" } })), a().createElement("div", { className: "react-chatbot-kit-chat-input-container" }, a().createElement("form", { className: "react-chatbot-kit-chat-input-form", onSubmit: function(e3) {
          if (e3.preventDefault(), b2 && "function" == typeof b2) {
            if (b2(x2)) {
              if (A2(), l2)
                return l2(x2);
              i2.parse(x2);
            }
          } else {
            if (A2(), l2)
              return l2(x2);
            i2.parse(x2);
          }
        } }, a().createElement("input", { className: "react-chatbot-kit-chat-input", placeholder: I, value: x2, onChange: function(e3) {
          return j2(e3.target.value);
        } }), a().createElement("button", { className: "react-chatbot-kit-chat-btn-send", style: B2 }, a().createElement(w, { className: "react-chatbot-kit-chat-btn-send-icon" }))))));
      }, O = function(e2) {
        var t2 = e2.message;
        return a().createElement("div", { className: "react-chatbot-kit-error" }, a().createElement("h1", { className: "react-chatbot-kit-error-header" }, "Ooops. Something is missing."), a().createElement("div", { className: "react-chatbot-kit-error-container" }, a().createElement(y, { message: t2, withAvatar: true, loading: false, id: 1, customStyles: { backgroundColor: "" }, messages: [] })), a().createElement("a", { href: "https://fredrikoseberg.github.io/react-chatbot-kit-docs/", rel: "noopener norefferer", target: "_blank", className: "react-chatbot-kit-error-docs" }, "View the docs"));
      };
      var k = function(e2) {
        return e2.widgets ? e2.widgets : [];
      }, M = function(e2) {
        try {
          new e2();
        } catch (e3) {
          return false;
        }
        return true;
      }, C = function() {
        return C = Object.assign || function(e2) {
          for (var t2, r2 = 1, a2 = arguments.length; r2 < a2; r2++)
            for (var n2 in t2 = arguments[r2])
              Object.prototype.hasOwnProperty.call(t2, n2) && (e2[n2] = t2[n2]);
          return e2;
        }, C.apply(this, arguments);
      };
      const N = function(e2, t2) {
        var r2 = this;
        this.addWidget = function(e3, t3) {
          var a2 = e3.widgetName, n2 = e3.widgetFunc, o2 = e3.mapStateToProps, s2 = e3.props;
          r2[a2] = { widget: n2, props: s2, mapStateToProps: o2, parentProps: C({}, t3) };
        }, this.getWidget = function(e3, t3) {
          var a2 = r2[e3];
          if (a2) {
            var n2, o2 = C(C(C(C({ scrollIntoView: t3.scrollIntoView }, a2.parentProps), "object" == typeof (n2 = a2.props) ? n2 : {}), r2.mapStateToProps(a2.mapStateToProps, t3)), { setState: r2.setState, actionProvider: r2.actionProvider || t3.actions, actions: t3.actions, state: t3, payload: t3.payload });
            return a2.widget(o2) || null;
          }
        }, this.mapStateToProps = function(e3, t3) {
          if (e3)
            return e3.reduce(function(e4, r3) {
              return e4[r3] = t3[r3], e4;
            }, {});
        }, this.setState = e2, this.actionProvider = t2;
      };
      var x = function() {
        return x = Object.assign || function(e2) {
          for (var t2, r2 = 1, a2 = arguments.length; r2 < a2; r2++)
            for (var n2 in t2 = arguments[r2])
              Object.prototype.hasOwnProperty.call(t2, n2) && (e2[n2] = t2[n2]);
          return e2;
        }, x.apply(this, arguments);
      }, j = function(e2, t2, r2) {
        if (r2 || 2 === arguments.length)
          for (var a2, n2 = 0, o2 = t2.length; n2 < o2; n2++)
            !a2 && n2 in t2 || (a2 || (a2 = Array.prototype.slice.call(t2, 0, n2)), a2[n2] = t2[n2]);
        return e2.concat(a2 || Array.prototype.slice.call(t2));
      };
      const T = function(e2) {
        var t2 = e2.config, n2 = e2.actionProvider, o2 = e2.messageParser, s2 = e2.messageHistory, c2 = e2.runInitialMessagesWithHistory, m2 = e2.saveMessages, g2 = function(e3, t3) {
          var r2 = {};
          for (var a2 in e3)
            Object.prototype.hasOwnProperty.call(e3, a2) && t3.indexOf(a2) < 0 && (r2[a2] = e3[a2]);
          if (null != e3 && "function" == typeof Object.getOwnPropertySymbols) {
            var n3 = 0;
            for (a2 = Object.getOwnPropertySymbols(e3); n3 < a2.length; n3++)
              t3.indexOf(a2[n3]) < 0 && Object.prototype.propertyIsEnumerable.call(e3, a2[n3]) && (r2[a2[n3]] = e3[a2[n3]]);
          }
          return r2;
        }(e2, ["config", "actionProvider", "messageParser", "messageHistory", "runInitialMessagesWithHistory", "saveMessages"]), d2 = "", f2 = "";
        if (!t2 || !n2 || !o2)
          return { configurationError: d2 = "I think you forgot to feed me some props. Did you remember to pass a config, a messageparser and an actionprovider?" };
        var h2 = function(e3, t3) {
          var r2 = [];
          return e3.initialMessages || r2.push("Config must contain property 'initialMessages', and it expects it to be an array of chatbotmessages."), r2;
        }(t2);
        if (h2.length)
          return { invalidPropsError: f2 = h2.reduce(function(e3, t3) {
            return e3 + t3;
          }, "") };
        var p2 = function(e3) {
          return e3.state ? e3.state : {};
        }(t2);
        s2 && Array.isArray(s2) ? t2.initialMessages = j([], s2, true) : "string" == typeof s2 && Boolean(s2) && (c2 || (t2.initialMessages = []));
        var v2, y2, b2, w2 = a().useState(x({ messages: j([], t2.initialMessages, true) }, p2)), E2 = w2[0], P2 = w2[1], S2 = a().useRef(E2.messages), O2 = a().useRef(), C2 = a().useRef();
        (0, r.useEffect)(function() {
          S2.current = E2.messages;
        }), (0, r.useEffect)(function() {
          s2 && Array.isArray(s2) && P2(function(e3) {
            return x(x({}, e3), { messages: s2 });
          });
        }, []), (0, r.useEffect)(function() {
          var e3 = C2.current;
          return function() {
            if (m2 && "function" == typeof m2) {
              var t3 = e3.innerHTML.toString();
              m2(S2.current, t3);
            }
          };
        }, []), (0, r.useEffect)(function() {
          O2.current = E2;
        }, [E2]);
        var T2 = n2, A2 = o2;
        return M(T2) && M(A2) ? (v2 = new n2(i, P2, u, O2.current, l, g2), y2 = new N(P2, v2), b2 = new o2(v2, O2.current), k(t2).forEach(function(e3) {
          return null == y2 ? void 0 : y2.addWidget(e3, g2);
        })) : (v2 = n2, b2 = o2, y2 = new N(P2, null), k(t2).forEach(function(e3) {
          return null == y2 ? void 0 : y2.addWidget(e3, g2);
        })), { widgetRegistry: y2, actionProv: v2, messagePars: b2, configurationError: d2, invalidPropsError: f2, state: E2, setState: P2, messageContainerRef: C2, ActionProvider: T2, MessageParser: A2 };
      };
      var A = function() {
        return A = Object.assign || function(e2) {
          for (var t2, r2 = 1, a2 = arguments.length; r2 < a2; r2++)
            for (var n2 in t2 = arguments[r2])
              Object.prototype.hasOwnProperty.call(t2, n2) && (e2[n2] = t2[n2]);
          return e2;
        }, A.apply(this, arguments);
      };
      const B = function(e2) {
        var t2 = e2.actionProvider, r2 = e2.messageParser, n2 = e2.config, o2 = e2.headerText, s2 = e2.placeholderText, c2 = e2.saveMessages, l2 = e2.messageHistory, u2 = e2.runInitialMessagesWithHistory, m2 = e2.disableScrollToBottom, g2 = e2.validator, d2 = function(e3, t3) {
          var r3 = {};
          for (var a2 in e3)
            Object.prototype.hasOwnProperty.call(e3, a2) && t3.indexOf(a2) < 0 && (r3[a2] = e3[a2]);
          if (null != e3 && "function" == typeof Object.getOwnPropertySymbols) {
            var n3 = 0;
            for (a2 = Object.getOwnPropertySymbols(e3); n3 < a2.length; n3++)
              t3.indexOf(a2[n3]) < 0 && Object.prototype.propertyIsEnumerable.call(e3, a2[n3]) && (r3[a2[n3]] = e3[a2[n3]]);
          }
          return r3;
        }(e2, ["actionProvider", "messageParser", "config", "headerText", "placeholderText", "saveMessages", "messageHistory", "runInitialMessagesWithHistory", "disableScrollToBottom", "validator"]), f2 = T(A({ config: n2, actionProvider: t2, messageParser: r2, messageHistory: l2, saveMessages: c2, runInitialMessagesWithHistory: u2 }, d2)), h2 = f2.configurationError, p2 = f2.invalidPropsError, v2 = f2.ActionProvider, y2 = f2.MessageParser, b2 = f2.widgetRegistry, w2 = f2.messageContainerRef, E2 = f2.actionProv, P2 = f2.messagePars, k2 = f2.state, C2 = f2.setState;
        if (h2)
          return a().createElement(O, { message: h2 });
        if (p2.length)
          return a().createElement(O, { message: p2 });
        var N2 = function(e3) {
          return e3.customStyles ? e3.customStyles : {};
        }(n2), x2 = function(e3) {
          return e3.customComponents ? e3.customComponents : {};
        }(n2), j2 = function(e3) {
          return e3.botName ? e3.botName : "Bot";
        }(n2), B2 = function(e3) {
          return e3.customMessages ? e3.customMessages : {};
        }(n2);
        return M(v2) && M(y2) ? a().createElement(S, { state: k2, setState: C2, widgetRegistry: b2, actionProvider: E2, messageParser: P2, customMessages: B2, customComponents: A({}, x2), botName: j2, customStyles: A({}, N2), headerText: o2, placeholderText: s2, validator: g2, messageHistory: l2, disableScrollToBottom: m2, messageContainerRef: w2 }) : a().createElement(v2, { state: k2, setState: C2, createChatBotMessage: i }, a().createElement(y2, null, a().createElement(S, { state: k2, setState: C2, widgetRegistry: b2, actionProvider: v2, messageParser: y2, customMessages: B2, customComponents: A({}, x2), botName: j2, customStyles: A({}, N2), headerText: o2, placeholderText: s2, validator: g2, messageHistory: l2, disableScrollToBottom: m2, messageContainerRef: w2 })));
      }, H = B;
      module.exports = t;
    })();
  }
});
export default require_build();
//# sourceMappingURL=react-chatbot-kit.js.map
